import 'package:flutter/material.dart';
import 'package:qsr_app/screens/menu_screen.dart';
import 'package:qsr_app/models/menu_item.dart';
import 'package:qsr_app/services/cart_service.dart';
import 'package:qsr_app/services/menu_service.dart';
import 'package:qsr_app/services/navigation_service.dart';
import 'package:qsr_app/services/user_preferences_service.dart';
import 'package:qsr_app/widgets/sauce_selection_dialog.dart';
import 'package:qsr_app/widgets/heat_level_selector.dart';
import 'package:qsr_app/widgets/custom_bottom_nav_bar.dart';
import 'package:qsr_app/screens/crew_pack_customization_screen.dart';
import 'package:qsr_app/models/crew_pack_selection.dart';
import 'package:qsr_app/screens/menu_item_extras_screen.dart';
import 'package:qsr_app/models/menu_extras.dart';
import 'package:qsr_app/models/combo_selection.dart';
import 'package:share_plus/share_plus.dart';
import '../constants/colors.dart';
import '../models/menu_category.dart'; // Import MenuCategory

class MenuItemScreen extends StatefulWidget {
  final String category;
  final CartService cartService;

  const MenuItemScreen({
    super.key, 
    required this.category,
    required this.cartService,
  });

  @override
  State<MenuItemScreen> createState() => _MenuItemScreenState();
}

class _MenuItemScreenState extends State<MenuItemScreen> {
  late Future<List<MenuItem>> _menuItemsFuture;
  late Future<List<MenuCategory>> _menuCategoriesFuture;
  final MenuService _menuService = MenuService();
  final UserPreferencesService _preferencesService = UserPreferencesService();
  final Map<String, String> _selectedSizes = {};
  final Set<String> _favoriteItems = {};

  /// Returns true if all required selections are made for this menu item
  bool _isAddToCartEnabled(MenuItem menuItem) {
    // Always enable for Crew Packs as they have different requirements
    if (widget.category == 'Crew Packs') {
      return true;
    }

    // Check heat level requirement
    if (menuItem.allowsHeatLevelSelection && menuItem.selectedHeatLevel == null) {
      return false;
    }

    // Check sauce selection requirement
    if (menuItem.allowsSauceSelection &&
        (menuItem.selectedSauces == null ||
         menuItem.selectedSauces!.length != menuItem.includedSauceCount)) {
      return false;
    }

    // Check bun selection requirement for Sandwiches using either selectedBunType or selectedSize
    if (widget.category == 'Sandwiches') {
      final hasBunSelection = menuItem.selectedBunType != null && menuItem.selectedBunType!.isNotEmpty;
      final hasSizeSelection = _selectedSizes.containsKey(menuItem.name) && _selectedSizes[menuItem.name]!.isNotEmpty;
      
      // If neither bun type nor size is selected for a sandwich, disable the button
      if (!hasBunSelection && !hasSizeSelection) {
        return false;
      }
    }

    // Check size selection requirement for Sides
    if (widget.category == 'Sides' && menuItem.sizes != null && menuItem.sizes!.isNotEmpty) {
      final hasSizeSelection = _selectedSizes.containsKey(menuItem.name) && _selectedSizes[menuItem.name]!.isNotEmpty;
      if (!hasSizeSelection) {
        return false;
      }
    }

    // All required selections are made
    return true;
  }

  @override
  void initState() {
    super.initState();
    _menuItemsFuture = _menuService.getMenuItems(widget.category);
    _menuCategoriesFuture = _menuService.getMenuCategories(); // Fetch categories
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    await _preferencesService.initialize();
    final preferences = _preferencesService.currentPreferences;
    if (preferences != null) {
      setState(() {
        _favoriteItems.addAll(preferences.favoriteMenuItems);
      });
    }
  }

  Future<void> _toggleFavorite(MenuItem menuItem) async {
    final isFavorite = _favoriteItems.contains(menuItem.id);

    if (isFavorite) {
      await _preferencesService.removeFromFavorites(menuItem.id);
      setState(() {
        _favoriteItems.remove(menuItem.id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${menuItem.name} removed from favorites'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.grey[600],
          ),
        );
      }
    } else {
      await _preferencesService.addToFavorites(menuItem.id);
      setState(() {
        _favoriteItems.add(menuItem.id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${menuItem.name} added to favorites'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.pink,
          ),
        );
      }
    }
  }

  Future<void> _shareMenuItem(MenuItem menuItem) async {
    final text = 'Check out this delicious ${menuItem.name} at Chicas Chicken!';
    const url = 'https://www.chicaschicken.com'; // Replace with a real URL if available
    await Share.share('$text\n$url');
  }

 Future<void> _handleCrewPackSelection(MenuItem crewPack) async {
    // Store ScaffoldMessenger reference before async operation
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CrewPackCustomizationScreen(crewPack: crewPack),
      ),
    );

    if (result != null && mounted) {
      // Extract the crew pack customization data
      final crewPackCustomization = result['sandwiches'] as CrewPackCustomization?;
      final allCustomizations = result['customizations'] as Map<String, List<dynamic>>?;
      final selectedSauces = result['sauces'] as List<String>?;

      // Update the crew pack with selected sauces
      if (selectedSauces != null) {
        crewPack.selectedSauces = selectedSauces;
      }

      // Separate regular customizations from dynamic customizations
      Map<String, List<MenuItem>>? regularCustomizations;
      Map<String, List<dynamic>>? dynamicCustomizations;

      if (allCustomizations != null) {
        regularCustomizations = <String, List<MenuItem>>{};
        dynamicCustomizations = <String, List<dynamic>>{};

        for (var entry in allCustomizations.entries) {
          final category = entry.key;
          final items = entry.value;

          List<MenuItem> regularItems = [];
          List<dynamic> dynamicItems = [];

          for (var item in items) {
            if (item is MenuItem) {
              regularItems.add(item);
            } else if (item is Map<String, dynamic> && item.containsKey('item')) {
              dynamicItems.add(item);
            }
          }

          if (regularItems.isNotEmpty) {
            regularCustomizations[category] = regularItems;
          }
          if (dynamicItems.isNotEmpty) {
            dynamicCustomizations[category] = dynamicItems;
          }
        }
      }

      // Add the crew pack with its customizations to the cart
      widget.cartService.addToCart(
        crewPack,
        customizations: regularCustomizations,
        dynamicCustomizations: dynamicCustomizations,
        crewPackCustomization: crewPackCustomization,
      );

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${crewPack.name} added to cart'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _handleSauceSelection(MenuItem menuItem) async {
    final result = await showDialog<List<String>>(
      context: context,
      builder: (BuildContext context) => SauceSelectionDialog(
        maxSauces: menuItem.includedSauceCount,
        initialSelections: menuItem.selectedSauces,
      ),
    );

    if (result != null) {
      setState(() {
        menuItem.selectedSauces = result;
      });
    }
  }

  Future<void> _handleHeatLevelSelection(MenuItem menuItem) async {
    final result = await showDialog<String>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Select Heat Level'),
        content: SizedBox(
          width: double.maxFinite,
          child: HeatLevelSelector(
            selectedHeatLevel: menuItem.selectedHeatLevel,
            onHeatLevelChanged: (heatLevel) {
              Navigator.pop(context, heatLevel);
            },
          ),
        ),
      ),
    );

    if (result != null) {
      setState(() {
        menuItem.selectedHeatLevel = result;
      });
    }
  }

  void _addToCart(MenuItem menuItem) {
    String? selectedSize = _selectedSizes[menuItem.name];

    // Check if item allows extras and should show extras screen
    if (menuItem.allowsExtras || _shouldShowExtrasForCategory(menuItem.category)) {
      _showExtrasScreen(menuItem, selectedSize);
      return;
    }

    // ✅ NEW: Enhanced validation for items requiring heat level
    if (menuItem.allowsHeatLevelSelection) {
      _validateHeatLevelItemRequirements(menuItem, selectedSize);
      return;
    }

    // ✅ NEW: Enhanced validation for items requiring sauce selection (chicken bites, whole wings)
    if (_requiresSauceSelection(menuItem)) {
      _validateSauceRequiredItemRequirements(menuItem, selectedSize);
      return;
    }

    // Check if sauce selection is required (for other items)
    if (menuItem.allowsSauceSelection &&
        (menuItem.selectedSauces == null ||
            menuItem.selectedSauces!.length != menuItem.includedSauceCount)) {
      // Show sauce selection dialog if sauces haven't been selected
      _handleSauceSelection(menuItem).then((_) {
        if (menuItem.selectedSauces?.length == menuItem.includedSauceCount) {
          _checkHeatLevelAndAddToCart(menuItem, selectedSize);
        }
      });
    } else {
      _checkHeatLevelAndAddToCart(menuItem, selectedSize);
    }
  }

  /// ✅ Check if item requires sauce selection (chicken bites, whole wings)
  /// Note: Items with heat level selection are handled by heat level validation instead
  bool _requiresSauceSelection(MenuItem menuItem) {
    // If item has heat level selection, it's handled by heat level validation
    if (menuItem.allowsHeatLevelSelection) {
      return false;
    }

    final category = menuItem.category.toLowerCase();
    return (category.contains('chicken bites') ||
            category.contains('whole wings') ||
            category == 'chicken bites' ||
            category == 'whole wings') &&
           menuItem.allowsSauceSelection;
  }

  /// ✅ NEW: Enhanced validation for items requiring sauce selection (chicken bites, whole wings)
  void _validateSauceRequiredItemRequirements(MenuItem menuItem, String? selectedSize) {
    final bool needsSauce = menuItem.selectedSauces == null ||
        menuItem.selectedSauces!.length != menuItem.includedSauceCount;

    if (needsSauce) {
      _showRequirementDialog(
        title: 'Sauce Selection Required',
        message: 'Please select ${menuItem.includedSauceCount} sauce${menuItem.includedSauceCount! > 1 ? 's' : ''} for ${menuItem.name}.',
        onConfirm: () => _handleSauceSelection(menuItem).then((_) {
          if (menuItem.selectedSauces?.length == menuItem.includedSauceCount) {
            _finalizeAddToCart(menuItem, selectedSize);
          }
        }),
      );
    } else {
      // Sauce is selected, proceed to cart
      _finalizeAddToCart(menuItem, selectedSize);
    }
  }

  /// ✅ NEW: Enhanced validation for heat level items requiring BOTH sauce AND heat level
  void _validateHeatLevelItemRequirements(MenuItem menuItem, String? selectedSize) {
    final bool needsSauce = menuItem.allowsSauceSelection &&
        (menuItem.selectedSauces == null || menuItem.selectedSauces!.length != menuItem.includedSauceCount);
    final bool needsHeatLevel = menuItem.selectedHeatLevel == null;

    if (needsSauce && needsHeatLevel) {
      // Both sauce and heat level are missing
      _showRequirementDialog(
        title: 'Selection Required',
        message: 'Please select both sauce and heat level for ${menuItem.name}.',
        onConfirm: () => _handleSauceSelectionFirst(menuItem, selectedSize),
      );
    } else if (needsSauce) {
      // Only sauce is missing
      _showRequirementDialog(
        title: 'Sauce Selection Required',
        message: 'Please select a sauce for ${menuItem.name}.',
        onConfirm: () => _handleSauceSelection(menuItem).then((_) {
          if (menuItem.selectedSauces?.length == menuItem.includedSauceCount) {
            _finalizeAddToCart(menuItem, selectedSize);
          }
        }),
      );
    } else if (needsHeatLevel) {
      // Only heat level is missing
      _showRequirementDialog(
        title: 'Heat Level Selection Required',
        message: 'Please select a heat level for ${menuItem.name}.',
        onConfirm: () => _handleHeatLevelSelection(menuItem).then((_) {
          if (menuItem.selectedHeatLevel != null) {
            _finalizeAddToCart(menuItem, selectedSize);
          }
        }),
      );
    } else {
      // Both are selected, proceed to cart
      _finalizeAddToCart(menuItem, selectedSize);
    }
  }

  /// Handle sauce selection first, then heat level for items requiring both
  Future<void> _handleSauceSelectionFirst(MenuItem menuItem, String? selectedSize) async {
    await _handleSauceSelection(menuItem);

    if (menuItem.selectedSauces?.length == menuItem.includedSauceCount) {
      // Sauce selected, now check heat level
      if (menuItem.selectedHeatLevel == null) {
        await _handleHeatLevelSelection(menuItem);
      }

      // Check if both are now selected
      if (menuItem.selectedSauces?.length == menuItem.includedSauceCount &&
          menuItem.selectedHeatLevel != null) {
        _finalizeAddToCart(menuItem, selectedSize);
      }
    }
  }

  /// Show requirement dialog with custom message
  void _showRequirementDialog({
    required String title,
    required String message,
    required VoidCallback onConfirm,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.deepOrange,
          ),
        ),
        content: Text(message),
        actions: <Widget>[
          TextButton(
            child: const Text('OK'),
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
          ),
        ],
      ),
    );
  }

  void _checkHeatLevelAndAddToCart(MenuItem menuItem, String? selectedSize) {
    // Check if heat level selection is required
    if (menuItem.allowsHeatLevelSelection && menuItem.selectedHeatLevel == null) {
      // Show heat level selection dialog if heat level hasn't been selected
      _handleHeatLevelSelection(menuItem).then((_) {
        if (menuItem.selectedHeatLevel != null) {
          _finalizeAddToCart(menuItem, selectedSize);
        }
      });
    } else {
      _finalizeAddToCart(menuItem, selectedSize);
    }
  }

  void _finalizeAddToCart(MenuItem menuItem, String? selectedSize) {
    widget.cartService.addToCart(menuItem, selectedSize: selectedSize);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${menuItem.name} added to cart'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  bool _shouldShowExtrasForCategory(String category) {
    // Categories that should show extras screen
    const extrasCategories = [
      'sandwiches',
      'crew packs',
      'whole wings',
      'chicken pieces',
      'chicken bites',
    ];
    return extrasCategories.contains(category.toLowerCase());
  }

  Future<void> _showExtrasScreen(MenuItem menuItem, String? selectedSize) async {
    // Store ScaffoldMessenger reference before async operation
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final result = await Navigator.of(context).push<dynamic>(
      MaterialPageRoute(
        builder: (context) => MenuItemExtrasScreen(
          menuItem: menuItem,
          initialSelectedSize: selectedSize,
        ),
      ),
    );

    if (result != null && mounted) {
      if (result is Map<String, dynamic>) {
        final comboMeal = result['comboMeal'] as ComboMeal?;
        final extras = result['extras'] as MenuItemExtras?;
        final specialInstructions = result['specialInstructions'] as String?;
        final resultSelectedSize = result['selectedSize'] as String?;

        if (comboMeal != null) {
          // Add combo meal to cart with extras and special instructions
          widget.cartService.addComboToCart(
            comboMeal,
            extras: extras,
            specialInstructions: specialInstructions,
          );

          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('${comboMeal.name} added to cart'),
              duration: const Duration(seconds: 2),
            ),
          );
        } else if (extras != null) {
          // Handle new format with extras and selected size (non-combo)
          // ✅ Validate heat level items before adding to cart
          if (menuItem.allowsHeatLevelSelection) {
            _validateHeatLevelItemBeforeAddingToCart(
              menuItem,
              resultSelectedSize ?? selectedSize,
              extras,
              scaffoldMessenger,
            );
          } else if (_requiresSauceSelection(menuItem)) {
            // ✅ Validate sauce-required items before adding to cart
            _validateSauceRequiredItemBeforeAddingToCart(
              menuItem,
              resultSelectedSize ?? selectedSize,
              extras,
              scaffoldMessenger,
            );
          } else {
            widget.cartService.addToCart(
              menuItem,
              selectedSize: resultSelectedSize ?? selectedSize,
              extras: extras,
              specialInstructions: specialInstructions, // Pass special instructions
            );

            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text('${menuItem.name} added to cart'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        }
      } else if (result is MenuItemExtras) {
        // This block handles cases where only MenuItemExtras is returned (older flow)
        // ✅ Validate heat level items before adding to cart
        if (menuItem.allowsHeatLevelSelection) {
          _validateHeatLevelItemBeforeAddingToCart(
            menuItem,
            selectedSize,
            result,
            scaffoldMessenger,
          );
        } else if (_requiresSauceSelection(menuItem)) {
          // ✅ Validate sauce-required items before adding to cart
          _validateSauceRequiredItemBeforeAddingToCart(
            menuItem,
            selectedSize,
            result,
            scaffoldMessenger,
          );
        } else {
          // Add item with extras to cart (non-heat level, non-sauce-required items)
          widget.cartService.addToCart(
            menuItem,
            selectedSize: selectedSize,
            extras: result,
          );

          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('${menuItem.name} added to cart'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  /// ✅ Validate heat level items from extras screen before adding to cart
  void _validateHeatLevelItemBeforeAddingToCart(
    MenuItem menuItem,
    String? selectedSize,
    MenuItemExtras? extras,
    ScaffoldMessengerState scaffoldMessenger,
  ) {
    final bool needsSauce = menuItem.allowsSauceSelection &&
        (menuItem.selectedSauces == null || menuItem.selectedSauces!.length != menuItem.includedSauceCount);
    final bool needsHeatLevel = menuItem.selectedHeatLevel == null;

    if (needsSauce && needsHeatLevel) {
      // Both sauce and heat level are missing
      _showRequirementDialog(
        title: 'Selection Required',
        message: 'Please select both sauce and heat level for ${menuItem.name}.',
        onConfirm: () => _handleSauceAndHeatForExtras(menuItem, selectedSize, extras, scaffoldMessenger),
      );
    } else if (needsSauce) {
      // Only sauce is missing
      _showRequirementDialog(
        title: 'Sauce Selection Required',
        message: 'Please select a sauce for ${menuItem.name}.',
        onConfirm: () => _handleSauceSelection(menuItem).then((_) {
          if (menuItem.selectedSauces?.length == menuItem.includedSauceCount) {
            _addToCartWithExtras(menuItem, selectedSize, extras, scaffoldMessenger);
          }
        }),
      );
    } else if (needsHeatLevel) {
      // Only heat level is missing
      _showRequirementDialog(
        title: 'Heat Level Selection Required',
        message: 'Please select a heat level for ${menuItem.name}.',
        onConfirm: () => _handleHeatLevelSelection(menuItem).then((_) {
          if (menuItem.selectedHeatLevel != null) {
            _addToCartWithExtras(menuItem, selectedSize, extras, scaffoldMessenger);
          }
        }),
      );
    } else {
      // Both are selected, proceed to cart
      _addToCartWithExtras(menuItem, selectedSize, extras, scaffoldMessenger);
    }
  }

  /// Handle sauce and heat level selection for extras items
  Future<void> _handleSauceAndHeatForExtras(
    MenuItem menuItem,
    String? selectedSize,
    MenuItemExtras? extras,
    ScaffoldMessengerState scaffoldMessenger,
  ) async {
    await _handleSauceSelection(menuItem);

    if (menuItem.selectedSauces?.length == menuItem.includedSauceCount) {
      // Sauce selected, now check heat level
      if (menuItem.selectedHeatLevel == null) {
        await _handleHeatLevelSelection(menuItem);
      }

      // Check if both are now selected
      if (menuItem.selectedSauces?.length == menuItem.includedSauceCount &&
          menuItem.selectedHeatLevel != null) {
        _addToCartWithExtras(menuItem, selectedSize, extras, scaffoldMessenger);
      }
    }
  }

  /// ✅ Validate sauce-required items from extras screen before adding to cart
  void _validateSauceRequiredItemBeforeAddingToCart(
    MenuItem menuItem,
    String? selectedSize,
    MenuItemExtras? extras,
    ScaffoldMessengerState scaffoldMessenger,
  ) {
    final bool needsSauce = menuItem.selectedSauces == null ||
        menuItem.selectedSauces!.length != menuItem.includedSauceCount;

    if (needsSauce) {
      _showRequirementDialog(
        title: 'Sauce Selection Required',
        message: 'Please select ${menuItem.includedSauceCount} sauce${menuItem.includedSauceCount! > 1 ? 's' : ''} for ${menuItem.name}.',
        onConfirm: () => _handleSauceSelection(menuItem).then((_) {
          if (menuItem.selectedSauces?.length == menuItem.includedSauceCount) {
            _addToCartWithExtras(menuItem, selectedSize, extras, scaffoldMessenger);
          }
        }),
      );
    } else {
      // Sauce is selected, proceed to cart
      _addToCartWithExtras(menuItem, selectedSize, extras, scaffoldMessenger);
    }
  }

  /// Add item with extras to cart after validation
  void _addToCartWithExtras(
    MenuItem menuItem,
    String? selectedSize,
    MenuItemExtras? extras,
    ScaffoldMessengerState scaffoldMessenger,
  ) {
    widget.cartService.addToCart(
      menuItem,
      selectedSize: selectedSize,
      extras: extras,
    );

    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text('${menuItem.name} added to cart'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildImageErrorFallback(BuildContext context, {bool isComingSoon = false}) {
    final text = isComingSoon ? 'Image Coming Soon' : 'Image Not Available';
    final icon = isComingSoon ? Icons.restaurant : Icons.restaurant_menu;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  const Color(0xFF0d2626).withValues(alpha: 0.9), // Dark teal
                  const Color(0xFFDC2626).withValues(alpha: 0.8), // Red accent
                ]
              : [
                  const Color(0xFFFF5C22).withValues(alpha: 0.8), // Orange
                  const Color(0xFF9B1C24).withValues(alpha: 0.8), // Red
                ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 60,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            const SizedBox(height: 8),
            Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeatLevelSelector(MenuItem menuItem) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1a3d3d).withValues(alpha: 0.8) // Dark teal with transparency for dark mode
            : Colors.red[50], // Light red for light mode
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFFDC2626).withValues(alpha: 0.5) // Red accent border for dark mode
              : Colors.red[200]!, // Light red border for light mode
          width: 2.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.whatshot,
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFFDC2626) // Red accent for dark mode
                    : Colors.red[600], // Red for light mode
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'HEAT LEVEL:',
                style: TextStyle(
                  fontFamily: 'MontserratBlack',
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white // White text for dark mode
                      : Colors.black87, // Dark text for light mode
                ),
              ),
              const Spacer(),
              // ✅ Required indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'REQUIRED',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Center(
            child: GestureDetector(
              onTap: () => _handleHeatLevelSelection(menuItem),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 28,
                ),
                decoration: BoxDecoration(
                  color: menuItem.selectedHeatLevel != null ? Colors.red[100] : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red[300]!),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      menuItem.selectedHeatLevel != null
                          ? Icons.local_fire_department
                          : Icons.add,
                      color: Colors.red[600],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        menuItem.selectedHeatLevel != null
                            ? '${menuItem.selectedHeatLevel!} (TAP TO CHANGE)'
                            : 'SELECT HEAT LEVEL',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.red[600],
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                        softWrap: true,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSauceSelector(MenuItem menuItem) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1a3d3d).withValues(alpha: 0.8) // Dark teal for dark mode
            : Colors.orange[50], // Light orange for light mode
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.orange[600]! // Orange accent for dark mode
              : Colors.orange[200]!, // Light orange for light mode
          width: 2.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.water_drop,
                color: Colors.orange[600],
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'SAUCE:',
                style: TextStyle(
                  fontFamily: 'MontserratBlack',
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white // White text for dark mode
                      : Colors.black87, // Dark text for light mode
                ),
              ),
              const Spacer(),
              // ✅ Required indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'REQUIRED',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Center(
            child: GestureDetector(
              onTap: () => _handleSauceSelection(menuItem),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 28,
                ),
                decoration: BoxDecoration(
                  color: menuItem.selectedSauces?.isNotEmpty == true ? Colors.orange[100] : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange[300]!),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      menuItem.selectedSauces?.isNotEmpty == true
                          ? Icons.check_circle
                          : Icons.add,
                      color: Colors.orange[600],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        menuItem.selectedSauces?.isNotEmpty == true
                            ? '${menuItem.selectedSauces!.join(", ")} (TAP TO CHANGE)'
                            : 'SELECT ${menuItem.includedSauceCount} SAUCE${menuItem.includedSauceCount! > 1 ? 'S' : ''}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.orange[600],
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                        softWrap: true,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.category.toUpperCase()),
        backgroundColor: Theme.of(context).primaryColor,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.9),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: () {
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => MenuScreen(cartService: widget.cartService),
              ),
              (route) => false,
            );
          },
        ),
      ),
      body: Column( // Use Column to stack the category header and menu items
        children: [
          // Category Header scrollable 2nd menu category header
          FutureBuilder<List<MenuCategory>>(
            future: _menuCategoriesFuture,
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                final categories = snapshot.data!;
                return Container(
                  margin: const EdgeInsets.only(top: 16.0), // Add this line to create space
                  height: 50, // Height of the category header
                  color: Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF1a3d3d) // Dark teal for dark mode
                      : Colors.grey[100], // Light gray for light mode
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal, // Enable horizontal scrolling
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      final category = categories[index];
                      final isSelected = category.name == widget.category;
                      return GestureDetector(
                        onTap: () {
                          // Navigate to a new MenuItemScreen with the selected category
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MenuItemScreen(
                                category: category.name,
                                cartService: widget.cartService,
                              ),
                            ),
                          );
                        },
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border(
                              bottom: BorderSide(
                                color: isSelected
                                    ? (Theme.of(context).brightness == Brightness.dark
                                        ? const Color(0xFFDC2626) // Red accent for dark mode
                                        : const Color.fromARGB(255, 135, 136, 90) // Green for light mode
                                     ): Colors.transparent,
                                width: 2,
                              ),
                            ),
                          ),
                          child: Text(
                            category.name,
                            style: TextStyle(
                              color: isSelected
                                  ? (Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white // White text for selected in dark mode
                                      : Colors.black) // Black text for selected in light mode
                                  : (Theme.of(context).brightness == Brightness.dark
                                      ? Colors.grey[400] // Light grey for unselected in dark mode
                                      : Colors.grey), // Grey for unselected in light mode
                              fontWeight: FontWeight.bold,
                              decoration: isSelected ? TextDecoration.underline : TextDecoration.none,
                              decorationColor: Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFFDC2626) // Red accent for dark mode
                                  : const Color.fromARGB(255, 135, 136, 90), // Green for light mode
                              decorationThickness: 2,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                );
              } else if (snapshot.hasError) {
                return Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: Text('Error loading categories: ${snapshot.error}'),
                );
              } else {
                return const SizedBox(
                  height: 50,
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }
            },
          ),
          Expanded( // Wrap the existing FutureBuilder with Expanded
            child: FutureBuilder<List<MenuItem>>(
              future: _menuItemsFuture,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final menuItems = snapshot.data!;
                  return ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: menuItems.length,
                    itemBuilder: (context, index) {
                      final menuItem = menuItems[index];
                      return Container(
                        margin: const EdgeInsets.only(bottom: 16.0),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF2d4a4a) // Dark teal card background for dark mode
                              : Colors.white, // White for light mode
                          borderRadius: BorderRadius.circular(16.0),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.black.withValues(alpha: 0.3) // Stronger shadow for dark mode
                                  : Colors.black.withValues(alpha: 0.1), // Light shadow for light mode
                              blurRadius: Theme.of(context).brightness == Brightness.dark ? 12.0 : 8.0,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Image Section
                              AspectRatio(
                                aspectRatio: 4 / 3,
                                child: Stack(
                                  fit: StackFit.expand,
                                  children: [
                                    ClipRRect(
                                      borderRadius: const BorderRadius.vertical(top: Radius.circular(16.0)),
                                      child: Builder(
                                        builder: (context) {
                                          // For the 'Sauces' category, always use the specified local asset.
                                          if (widget.category == 'Sauces') {
                                            return Image.asset(
                                              'assets/images/MENU/SAUCES/Sauces.jpg',
                                              fit: BoxFit.cover,
                                              errorBuilder: (context, error, stackTrace) =>
                                                  _buildImageErrorFallback(context),
                                            );
                                          }
                                          // For other categories, use the imageUrl from the data.
                                          if (menuItem.imageUrl.isNotEmpty) {
                                            return Image.network(
                                              menuItem.imageUrl,
                                              fit: BoxFit.cover,
                                              errorBuilder: (context, error, stackTrace) =>
                                                  _buildImageErrorFallback(context),
                                            );
                                          }
                                          // Fallback if imageUrl is empty.
                                          return _buildImageErrorFallback(context, isComingSoon: true);
                                        },
                                      ),
                                    ),
                                    // Favorite button is now moved next to the price
                                  ],
                                ),
                              ),

                              // Content Section
                              Padding(
                                padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 60.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Title
                                    Text(
                                      menuItem.name.toUpperCase(),
                                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        fontFamily: 'MontserratBlack',
                                        fontWeight: FontWeight.bold,
                                        color: FoodItemTextColors.itemNameColor,
                                      ),
                                    ),
                                    const SizedBox(height: 8),

                                    // Description
                                    Text(
                                      menuItem.description.toUpperCase(),
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? Colors.white70 // Lighter white for description
                                            : Colors.black54, // Darker grey for description
                                        height: 1.4,
                                      ),
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 8),
                                    // Price, Favorite and Share Buttons
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          '\$${menuItem.price.toStringAsFixed(2)}',
                                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                            fontFamily: 'MontserratBlack',
                                            fontWeight: FontWeight.bold,
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? Colors.white // White text for dark mode
                                                : AppColors.chicaOrange, // Dark text for light mode
                                          ),
                                        ),
                                        Row(
                                          children: [
                                            GestureDetector(
                                              onTap: () => _toggleFavorite(menuItem),
                                              child: Icon(
                                                _favoriteItems.contains(menuItem.id)
                                                    ? Icons.favorite
                                                    : Icons.favorite_border,
                                                color: _favoriteItems.contains(menuItem.id)
                                                    ? Colors.pink
                                                    : Colors.grey,
                                                size: 30,
                                              ),
                                            ),
                                            const SizedBox(width: 16),
                                            GestureDetector(
                                              onTap: () => _shareMenuItem(menuItem),
                                              child: const Icon(
                                                Icons.share,
                                                color: Colors.grey,
                                                size: 30,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),                                    
                                    const SizedBox(height: 16),
                                    // Size Selection (if available)
                                    if (menuItem.sizes != null) ...[
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context).brightness == Brightness.dark
                                              ? const Color(0xFF1a3d3d) // Dark teal for dark mode
                                              : const Color(0xFFFFC107).withValues(alpha: 0.3), // yellow for light mode
                                          borderRadius: BorderRadius.circular(8),
                                          border: Border.all(
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? const Color(0xFFFFC107) // yellow border for dark mode
                                                : const Color(0xFFFFC107), // yellow border for light mode
                                            width: 2.0,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Icon(
                                                  menuItem.category.toLowerCase() == 'sides'
                                                      ? Icons.aspect_ratio // Icon for size
                                                      : Icons.lunch_dining, // Icon for bun
                                                  color: Theme.of(context).brightness == Brightness.dark
                                                      ? const Color.fromARGB(255, 255, 152, 55) // White for dark mode
                                                      : (menuItem.category.toLowerCase() == 'sides' ? Colors.black87 : const Color.fromARGB(255, 255, 152, 55) ), // Brown for bun icon in light mode
                                                  size: 18,
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  menuItem.category.toLowerCase() == 'sides'
                                                      ? 'CHOOSE SIZE:'
                                                      : 'CHOOSE BUN:',
                                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                    fontFamily: 'MontserratBlack',
                                                    fontWeight: FontWeight.w600,
                                                    color: Theme.of(context).brightness == Brightness.dark
                                                        ? Colors.white // White text for dark mode
                                                        : Colors.black87, // Dark text for light mode
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 8),
                                            Row(
                                              children: menuItem.sizes!.keys.map((size) {
                                                final isSelected = _selectedSizes[menuItem.name] == size;
                                                return Expanded(
                                                  child: GestureDetector(
                                                    onTap: () {
                                                      setState(() {
                                                        _selectedSizes[menuItem.name] = size;
                                                        menuItem.price = menuItem.sizes![size]!;
                                                      });
                                                    },
                                                    child: Container(
                                                      margin: const EdgeInsets.symmetric(horizontal: 4),
                                                      padding: const EdgeInsets.symmetric(
                                                        vertical: 12,
                                                      ),
                                                      decoration: BoxDecoration(
                                                        color: isSelected
                                                            ? const Color(0xFFFFC107).withValues(alpha: 0.3) // chica's orange accent for selected in both themes
                                                            : (Theme.of(context).brightness == Brightness.dark
                                                                ? const Color(0xFF2d4a4a) // Dark teal for unselected in dark mode
                                                                : Colors.white), // White for unselected in light mode
                                                        borderRadius: BorderRadius.circular(12),
                                                        border: Border.all(
                                                          color: isSelected
                                                              ? const Color(0xFFFFC107) // chica's orange accent border for selected
                                                              : (Theme.of(context).brightness == Brightness.dark
                                                                  ? const Color(0xFF1a3d3d) // Dark teal border for dark mode
                                                                  : const Color(0xFFFFC107)), // yellow border for light mode
                                                        ),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          size == 'Brioche Bun' ? '${size.toUpperCase()} (+\$1)' : size.toUpperCase(),
                                                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                                            color: isSelected
                                                                ? const Color.fromARGB(255, 0, 0, 0) // White text for selected
                                                                : (Theme.of(context).brightness == Brightness.dark
                                                                    ? Colors.white // White text for dark mode
                                                                    : Colors.black87), // Dark text for light mode
                                                            fontWeight: FontWeight.w500,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              }).toList(),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                    ],

                                    // Heat Level Selection (if available)
                                    if (menuItem.allowsHeatLevelSelection) ...[
                                      _buildHeatLevelSelector(menuItem),
                                      const SizedBox(height: 16),
                                    ],

                                    // ✅ Sauce Selection (now a unified widget)
                                    if ((menuItem.allowsHeatLevelSelection && menuItem.allowsSauceSelection) || _requiresSauceSelection(menuItem)) ...[
                                      _buildSauceSelector(menuItem),
                                      const SizedBox(height: 16),
                                    ],

                                    // Action Button
                                    SizedBox(
                                      width: double.infinity,
                                      height: 75,
                                      child: ElevatedButton(
                                        onPressed: widget.category == 'Crew Packs'
                                            ? () => _handleCrewPackSelection(menuItem)
                                            : (_isAddToCartEnabled(menuItem)
                                                ? () => _addToCart(menuItem)
                                                : null),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: const Color(0xFFFF5C22),
                                          foregroundColor: Colors.white,
                                          elevation: 2,
                                          shadowColor: const Color(0xFFFF5C22).withValues(alpha: 0.3),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              widget.category == 'Crew Packs'
                                                  ? Icons.tune
                                                  : Icons.add_shopping_cart,
                                              size: 20,
                                            ),
                                            const SizedBox(width: 8),                                              Text(
                                              widget.category == 'Crew Packs'
                                                  ? 'CUSTOMIZE PACK'
                                                  : 'ADD TO CART',
                                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                fontWeight: FontWeight.w600,
                                                color: _isAddToCartEnabled(menuItem)
                                                    ? Colors.white
                                                    : Colors.white.withValues(alpha: 0.5),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                } else if (snapshot.hasError) {
                  return Center(
                    child: Text('Error: ${snapshot.error}'),
                  );
                } else {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: CustomBottomNavBar(
        selectedIndex: 2, // Menu is selected (now at index 2)
        cartService: widget.cartService,
        onItemSelected: (index) {
          // Navigate to the correct page using navigation service
          switch (index) {
            case 0:
              NavigationService.navigateToHome();
              break;
            case 1:
              NavigationService.navigateToScan();
              break;
            case 2:
              NavigationService.navigateToMenu();
              break;
            case 3:
              NavigationService.navigateToCart();
              break;
            case 4:
              NavigationService.navigateToMore();
              break;
          }
        },
      ),
    );
  }
}