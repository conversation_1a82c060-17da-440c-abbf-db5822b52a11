Resolving dependencies...
Downloading packages...
  _fe_analyzer_shared 61.0.0 (88.0.0 available)
  _flutterfire_internals 1.3.35 (1.3.60 available)
  analyzer 5.13.0 (8.1.1 available)
  archive 3.6.1 (4.0.7 available)
  audioplayers 5.2.1 (6.5.0 available)
  audioplayers_android 4.0.3 (5.2.1 available)
  audioplayers_darwin 5.0.2 (6.3.0 available)
  audioplayers_linux 3.1.0 (4.2.1 available)
  audioplayers_platform_interface 6.1.0 (7.1.1 available)
  audioplayers_web 4.1.0 (5.1.1 available)
  audioplayers_windows 3.1.0 (4.2.1 available)
  build 2.4.1 (3.0.2 available)
  build_config 1.1.2 (1.2.0 available)
  build_resolvers 2.4.2 (3.0.2 available)
  build_runner 2.4.13 (2.7.0 available)
  build_runner_core 7.3.2 (9.3.0 available)
  cached_network_image 3.4.0 (3.4.1 available)
  cached_network_image_web 1.3.0 (1.3.1 available)
  characters 1.4.0 (1.4.1 available)
  connectivity_plus 5.0.2 (6.1.5 available)
  connectivity_plus_platform_interface 1.2.4 (2.0.1 available)
  dart_style 2.3.2 (3.1.2 available)
  device_info_plus 10.1.2 (11.5.0 available)
  dio 5.8.0+1 (5.9.0 available)
  file_selector_macos 0.9.4+3 (0.9.4+4 available)
  firebase_core 2.32.0 (4.0.0 available)
  firebase_core_platform_interface 5.4.0 (6.0.0 available)
  firebase_core_web 2.17.5 (3.0.0 available)
  firebase_messaging 14.9.4 (16.0.0 available)
  firebase_messaging_platform_interface 4.5.37 (4.7.0 available)
  firebase_messaging_web 3.8.7 (4.0.0 available)
  flutter_image_compress 2.3.0 (2.4.0 available)
  flutter_image_compress_web 0.1.4+1 (0.1.5 available)
  flutter_lints 3.0.2 (6.0.0 available)
  flutter_local_notifications 16.3.3 (19.4.0 available)
  flutter_local_notifications_linux 4.0.1 (6.0.0 available)
  flutter_local_notifications_platform_interface 7.2.0 (9.1.0 available)
  flutter_plugin_android_lifecycle 2.0.28 (2.0.29 available)
  flutter_secure_storage_linux 1.2.3 (2.0.1 available)
  flutter_secure_storage_macos 3.1.3 (4.0.0 available)
  flutter_secure_storage_platform_interface 1.1.2 (2.0.1 available)
  flutter_secure_storage_web 1.2.1 (2.0.0 available)
  flutter_secure_storage_windows 3.1.2 (4.0.0 available)
  flutter_tts 3.8.5 (4.2.3 available)
  http 1.4.0 (1.5.0 available)
  image 4.3.0 (4.5.4 available)
  image_picker 1.1.2 (1.2.0 available)
  image_picker_android 0.8.12+23 (0.8.13 available)
  image_picker_for_web 3.0.6 (3.1.0 available)
  image_picker_ios 0.8.12+2 (0.8.13 available)
  image_picker_linux 0.2.1+2 (0.2.2 available)
  image_picker_macos 0.2.1+2 (0.2.2 available)
  image_picker_platform_interface 2.10.1 (2.11.0 available)
  image_picker_windows 0.2.1+1 (0.2.2 available)
  js 0.6.7 (0.7.2 available)
  lints 3.0.0 (6.0.0 available)
  material_color_utilities 0.11.1 (0.13.0 available)
  meta 1.16.0 (1.17.0 available)
  mime 1.0.6 (2.0.0 available)
  mockito 5.4.4 (5.5.0 available)
  path_provider_foundation 2.4.1 (2.4.2 available)
  permission_handler 11.4.0 (12.0.1 available)
  permission_handler_android 12.1.0 (13.0.1 available)
  petitparser 6.1.0 (7.0.1 available)
  process 5.0.3 (5.0.5 available)
  provider 6.1.5 (6.1.5+1 available)
  share_plus 7.2.2 (11.1.0 available)
  share_plus_platform_interface 3.4.0 (6.1.0 available)
  shared_preferences_android 2.4.10 (2.4.11 available)
  shelf_web_socket 2.0.1 (3.0.0 available)
  source_gen 1.5.0 (3.1.0 available)
  sqflite_android 2.4.1 (2.4.2+2 available)
  sqflite_common 2.5.5 (2.5.6 available)
  test_api 0.7.6 (0.7.7 available)
  timezone 0.9.4 (0.10.1 available)
  url_launcher 6.3.1 (6.3.2 available)
  url_launcher_android 6.3.16 (6.3.17 available)
  url_launcher_ios 6.3.3 (6.3.4 available)
  url_launcher_macos 3.2.2 (3.2.3 available)
  vibration 1.9.0 (3.1.3 available)
  vibration_platform_interface 0.0.3 (0.1.0 available)
  video_player_android 2.8.7 (2.8.12 available)
  video_player_avfoundation 2.8.0 (2.8.4 available)
  vm_service 15.0.0 (15.0.2 available)
  web 0.5.1 (1.1.1 available)
  web_socket_channel 2.4.0 (3.0.3 available)
  win32_registry 1.1.5 (2.1.0 available)
  xml 6.5.0 (6.6.1 available)
Got dependencies!
88 packages have newer versions incompatible with dependency constraints.
Try `flutter pub outdated` for more information.
Analyzing Chicas Chicken Flutter...                             

warning - The declaration '_navigateToMenu' isn't referenced - integration_test\flows\order_flow_test.dart:13:14 - unused_element
warning - The declaration '_browseMenuCategories' isn't referenced - integration_test\flows\order_flow_test.dart:45:14 - unused_element
warning - The declaration '_selectMenuItems' isn't referenced - integration_test\flows\order_flow_test.dart:67:14 - unused_element
warning - The declaration '_addExtrasAndModifications' isn't referenced - integration_test\flows\order_flow_test.dart:83:14 - unused_element
warning - The declaration '_reviewCart' isn't referenced - integration_test\flows\order_flow_test.dart:111:14 - unused_element
warning - The declaration '_proceedToCheckout' isn't referenced - integration_test\flows\order_flow_test.dart:130:14 - unused_element
warning - The declaration '_verifyOrderSummary' isn't referenced - integration_test\flows\order_flow_test.dart:139:14 - unused_element
warning - The declaration '_testCategoryNavigation' isn't referenced - integration_test\flows\order_flow_test.dart:152:14 - unused_element
warning - The declaration '_testSearchFunctionality' isn't referenced - integration_test\flows\order_flow_test.dart:167:14 - unused_element
warning - The declaration '_testItemDetailsView' isn't referenced - integration_test\flows\order_flow_test.dart:179:14 - unused_element
warning - The declaration '_addMultipleItemsToCart' isn't referenced - integration_test\flows\order_flow_test.dart:191:14 - unused_element
warning - The declaration '_testCartOperations' isn't referenced - integration_test\flows\order_flow_test.dart:215:14 - unused_element
warning - The declaration '_selectCustomizableItem' isn't referenced - integration_test\flows\order_flow_test.dart:238:14 - unused_element
warning - The declaration '_testCustomizationOptions' isn't referenced - integration_test\flows\order_flow_test.dart:254:14 - unused_element
warning - The declaration '_verifyCustomizationsInCart' isn't referenced - integration_test\flows\order_flow_test.dart:277:14 - unused_element
   info - Don't use 'BuildContext's across async gaps - lib\screens\crew_pack_customization_screen.dart:119:9 - use_build_context_synchronously
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\favorites_screen.dart:169:42 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\favorites_screen.dart:268:42 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:689:43 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:690:43 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:693:43 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:694:43 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:705:35 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:712:37 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:730:39 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:735:41 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:842:39 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:960:35 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:963:37 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:1099:50 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:1100:50 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:1240:73 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:1301:87 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\screens\menu_item_screen.dart:1384:72 - deprecated_member_use
   info - 'scale' is deprecated and shouldn't be used. Use scaleByVector3, scaleByVector4, or scaleByDouble instead - lib\screens\order_type_selection_screen.dart:236:15 - deprecated_member_use
   info - 'scale' is deprecated and shouldn't be used. Use scaleByVector3, scaleByVector4, or scaleByDouble instead - lib\screens\order_type_selection_screen.dart:349:15 - deprecated_member_use
   info - 'groupValue' is deprecated and shouldn't be used. Use a RadioGroup ancestor to manage group value instead. This feature was deprecated after v3.32.0-0.0.pre - lib\services\theme_service.dart:274:11 - deprecated_member_use
   info - 'onChanged' is deprecated and shouldn't be used. Use RadioGroup to handle value change instead. This feature was deprecated after v3.32.0-0.0.pre - lib\services\theme_service.dart:275:11 - deprecated_member_use
   info - Invalid use of a private type in a public API - lib\widgets\animated_menu_filter_bar.dart:18:3 - library_private_types_in_public_api
   info - 'dialogBackgroundColor' is deprecated and shouldn't be used. Use DialogThemeData.backgroundColor instead. This feature was deprecated after v3.27.0-0.1.pre - lib\widgets\heat_level_selector.dart:84:47 - deprecated_member_use
   info - 'groupValue' is deprecated and shouldn't be used. Use a RadioGroup ancestor to manage group value instead. This feature was deprecated after v3.32.0-0.0.pre - lib\widgets\language_selector.dart:30:15 - deprecated_member_use
   info - 'onChanged' is deprecated and shouldn't be used. Use RadioGroup to handle value change instead. This feature was deprecated after v3.32.0-0.0.pre - lib\widgets\language_selector.dart:31:15 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\widgets\navigation_menu_drawer.dart:114:83 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\widgets\navigation_menu_drawer.dart:178:83 - deprecated_member_use
   info - 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss - lib\widgets\navigation_menu_drawer.dart:270:66 - deprecated_member_use
   info - 'groupValue' is deprecated and shouldn't be used. Use a RadioGroup ancestor to manage group value instead. This feature was deprecated after v3.32.0-0.0.pre - lib\widgets\sauce_selection_dialog.dart:214:33 - deprecated_member_use
   info - 'onChanged' is deprecated and shouldn't be used. Use RadioGroup to handle value change instead. This feature was deprecated after v3.32.0-0.0.pre - lib\widgets\sauce_selection_dialog.dart:215:33 - deprecated_member_use
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:14:3 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:19:3 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:20:3 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:21:3 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:29:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:48:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:52:7 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:62:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:149:5 - avoid_print
warning - The value of the local variable 'colorValue' isn't used - scripts\accessibility_audit.dart:161:13 - unused_local_variable
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:177:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:181:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:226:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:230:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:234:7 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:258:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:262:5 - avoid_print
   info - Don't invoke 'print' in production code - scripts\accessibility_audit.dart:273:5 - avoid_print
warning - The value of the local variable 'buttonWidget' isn't used - test\accessibility\accessibility_test_suite.dart:113:17 - unused_local_variable
   info - 'hasFlag' is deprecated and shouldn't be used. Use flagsCollection instead. This feature was deprecated after v3.32.0-0.0.pre - test\accessibility\accessibility_test_suite.dart:179:28 - deprecated_member_use
   info - 'hasFlag' is deprecated and shouldn't be used. Use flagsCollection instead. This feature was deprecated after v3.32.0-0.0.pre - test\accessibility\accessibility_test_suite.dart:250:28 - deprecated_member_use
warning - The value of the local variable 'errorAnnounced' isn't used - test\accessibility\accessibility_test_suite.dart:339:14 - unused_local_variable
warning - The value of the local variable 'semanticsNodes' isn't used - test\accessibility\accessibility_test_suite.dart:469:9 - unused_local_variable
   info - 'pipelineOwner' is deprecated and shouldn't be used. Interact with the pipelineOwner tree rooted at RendererBinding.rootPipelineOwner instead. Or instead of accessing the SemanticsOwner of any PipelineOwner interact with the SemanticsBinding directly. This feature was deprecated after v3.10.0-12.0.pre - test\accessibility\accessibility_test_suite.dart:469:41 - deprecated_member_use

71 issues found. (ran in 8.9s)
