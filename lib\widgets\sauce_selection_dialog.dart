import 'package:flutter/material.dart';
import 'package:qsr_app/constants/colors.dart';

class SauceSelectionDialog extends StatefulWidget {
  final int? maxSauces;
  final List<String>? initialSelections;
  final Color? backgroundColor; // Add background color parameter

  const SauceSelectionDialog({
    super.key,
    required this.maxSauces,
    this.initialSelections,
    this.backgroundColor, // New optional parameter for background color
  });

  @override
  State<SauceSelectionDialog> createState() => _SauceSelectionDialogState();
}

class _SauceSelectionDialogState extends State<SauceSelectionDialog> {
  final List<String> _selectedSauces = [];
  
  // Define sauce options with their descriptions
  final List<Map<String, String>> _sauceOptions = [
    {
      'name': "Chica's Sauce",
      'description': 'BUTTERMILK RANCH',
    },
    {
      'name': 'Sweet Heat',
      'description': 'SWEET & SPICY',
    },
    {
      'name': 'Buffalo',
      'description': 'CLASSIC SPICY',
    },
    {
      'name': 'Hot Honey',
      'description': 'SWEET & HOT',
    },
    {
      'name': '<PERSON>otle <PERSON>oli',
      'description': 'SMOKY SPICE',
    },
    {
      'name': 'BBQ',
      'description': 'SWEET & TANGY',
    },
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialSelections != null) {
      _selectedSauces.addAll(widget.initialSelections!);
    }
  }

  bool _canSelectMoreSauces() {
    return widget.maxSauces == null || _selectedSauces.length < widget.maxSauces!;
  }

  void _toggleSauceSelection(String sauceName) {
    setState(() {
      if (_selectedSauces.contains(sauceName)) {
        _selectedSauces.remove(sauceName);
      } else {
        if (widget.maxSauces == 1) {
          // If max is 1, replace the current selection
          _selectedSauces.clear();
          _selectedSauces.add(sauceName);
        } else if (_canSelectMoreSauces()) {
          // Add if under the limit
          _selectedSauces.add(sauceName);
        } else {
          // At limit, replace the first sauce with the new selection
          _selectedSauces.removeAt(0);
          _selectedSauces.add(sauceName);
        }
      }
    });
  }

  Widget _buildCircularCheckbox({
    required bool value,
    required ValueChanged<bool?> onChanged,
    Color activeColor = Colors.orange,
  }) {
    return InkWell(
      onTap: () => onChanged(!value),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: value ? activeColor : const Color(0xFF757575),
            width: 2,
          ),
          color: value ? activeColor : const Color(0xFF7A3B00),
        ),
        child: value
            ? const Icon(
                Icons.check,
                size: 16,
                color: Colors.white,
              )
            : null,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // More robust detection: check several theme background colors so we
    // correctly detect dark dialogs even when brightness is not set to dark.
    final theme = Theme.of(context);
    bool isColorDark(Color? c) => c != null && c.computeLuminance() < 0.5;
    final bool isBrightnessDark = theme.brightness == Brightness.dark;
    final bool isBgDark = isColorDark(theme.dialogTheme.backgroundColor) ||
        isColorDark(theme.colorScheme.surface) ||
        isColorDark(theme.colorScheme.surface) ||
        isColorDark(theme.scaffoldBackgroundColor);
    final bool isDark = isBrightnessDark || isBgDark;

    final bool isMultiSelect = widget.maxSauces != null && widget.maxSauces! > 1;
    
    return AlertDialog(
      backgroundColor: widget.backgroundColor,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Sauce${isMultiSelect ? 's' : ''}',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (isMultiSelect) ...[
            const SizedBox(height: 8),
            Text(
              'Select up to ${widget.maxSauces} sauces (${_selectedSauces.length} selected)',
              style: TextStyle(
                fontSize: 16,
                color: isDark ? Colors.white70 : const Color(0xFF7A3B00),
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _sauceOptions.map((sauce) {
              final isSelected = _selectedSauces.contains(sauce['name']);
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: InkWell(
                  onTap: () => _toggleSauceSelection(sauce['name']!),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.orange.withValues(alpha: 0.1) : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? Colors.orange : Colors.grey.shade300,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.water_drop,
                                    color: isSelected ? Colors.orange : AppColors.chicaOrange,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    sauce['name']!,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: isSelected ? Colors.orange : (isDark ? Colors.white : Colors.black87),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Padding(
                                padding: const EdgeInsets.only(left: 24),
                                child: Text(
                                  sauce['description']!,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: isDark ? Colors.white70 : const Color(0xFF7A3B00),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        widget.maxSauces == 1
                            ? Radio<String>(
                                value: sauce['name']!,
                                groupValue: _selectedSauces.isNotEmpty ? _selectedSauces.first : null,
                                onChanged: (value) {
                                  if (value != null) {
                                    _toggleSauceSelection(value);
                                  }
                                },
                                activeColor: Colors.orange,
                              )
                            : _buildCircularCheckbox(
                                value: isSelected,
                                onChanged: (bool? value) {
                                  if (value != null) {
                                    _toggleSauceSelection(sauce['name']!);
                                  }
                                },
                              ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'CANCEL',
            style: TextStyle(color: isDark ? Colors.white70 : Colors.black87),
          ),
        ),
        ElevatedButton(
          onPressed: _selectedSauces.isNotEmpty &&
                    (!isMultiSelect || _selectedSauces.length == widget.maxSauces)
              ? () => Navigator.pop(context, _selectedSauces)
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          child: const Text('CONFIRM'),
        ),
      ],
    );
  }
}