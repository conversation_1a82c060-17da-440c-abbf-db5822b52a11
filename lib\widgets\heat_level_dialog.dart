import 'package:flutter/material.dart';
import 'package:qsr_app/constants/heat_levels.dart';

/// ✅ Reusable Heat Level Selection Dialog for Cart Editing
class HeatLevelDialog extends StatefulWidget {
  final String? currentHeatLevel;
  final Function(String) onHeatLevelSelected;
  final String itemName;

  const HeatLevelDialog({
    super.key,
    required this.currentHeatLevel,
    required this.onHeatLevelSelected,
    required this.itemName,
  });

  @override
  State<HeatLevelDialog> createState() => _HeatLevelDialogState();
}

class _HeatLevelDialogState extends State<HeatLevelDialog> {
  String? _selectedHeatLevel;

  @override
  void initState() {
    super.initState();
    _selectedHeatLevel = widget.currentHeatLevel;
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return AlertDialog(
      backgroundColor: isDark ? const Color(0xFF2d4a4a) : Colors.white,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.whatshot,
                color: Colors.red[600],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'CHANGE HEAT LEVEL',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.itemName,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ...HeatLevels.all.map((heatLevel) => _buildHeatLevelOption(heatLevel)),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'CANCEL',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)),
          ),
        ),
        ElevatedButton(
          onPressed: _selectedHeatLevel != null
              ? () {
                  widget.onHeatLevelSelected(_selectedHeatLevel!);
                  Navigator.of(context).pop();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
          ),
          child: const Text('UPDATE HEAT LEVEL'),
        ),
      ],
    );
  }

  // Dark mode teal colors
  static const Color darkModeTeal = Color(0xFF00BFA5); // Brighter teal for selection
  static const Color darkModeTealSurface = Color(0xFF00796B); // Base teal for surface

  Widget _buildHeatLevelOption(HeatLevel heatLevel) {
    final isSelected = _selectedHeatLevel == heatLevel.name;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Define theme-aware text colors to ensure readability in dark mode.
    final Color textColor;
    final Color descriptionColor;

    if (isSelected) {
      textColor = heatLevel.color;
      descriptionColor = heatLevel.color.withValues(alpha: 0.8);
    } else {
      if (isDark) {
        textColor = Colors.white;
        descriptionColor = Colors.white70;
      } else {
        textColor = Theme.of(context).colorScheme.onSurface;
        descriptionColor = Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7);
      }
    }
    
    // Compute background color so the BoxDecoration shows teal in dark mode
    final Color bgColor = isDark
        ? (isSelected ? darkModeTeal.withAlpha(40) : darkModeTealSurface)
        : (isSelected ? heatLevel.color.withAlpha(40) : Colors.white);

    // Use InkWell inside a Material for ripple but apply background on the BoxDecoration
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedHeatLevel = heatLevel.name;
            });
          },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: bgColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? (isDark ? darkModeTeal : heatLevel.color)
                    : (isDark ? darkModeTeal.withAlpha(150) : Colors.grey.shade300),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                // Heat level icon
                Icon(
                  heatLevel.icon,
                  color: heatLevel.color,
                  size: 20,
                ),
                const SizedBox(width: 12),
                
                // Heat level info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        heatLevel.name,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                      Text(
                        heatLevel.description,
                        style: TextStyle(
                          fontSize: 11,
                          color: descriptionColor,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Flame rating
                Row(
                  children: HeatLevels.buildFlameRating(heatLevel.stars, size: 14),
                ),
                
                // Selection indicator
                if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    child: Icon(
                      Icons.check_circle,
                      color: heatLevel.color,
                      size: 20,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

}
