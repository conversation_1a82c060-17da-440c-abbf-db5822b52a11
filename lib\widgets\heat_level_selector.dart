import 'package:flutter/material.dart';
import '../constants/heat_levels.dart';

class HeatLevelSelector extends StatefulWidget {
  final String? selectedHeatLevel;
  final Function(String?) onHeatLevelChanged;

  const HeatLevelSelector({
    super.key,
    this.selectedHeatLevel,
    required this.onHeatLevelChanged,
  });

  @override
  State<HeatLevelSelector> createState() => _HeatLevelSelectorState();
}

class _HeatLevelSelectorState extends State<HeatLevelSelector> {
  String? _selectedLevel;

  @override
  void initState() {
    super.initState();
    _selectedLevel = widget.selectedHeatLevel;
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...HeatLevels.all.asMap().entries.map((entry) {
                final heatLevel = entry.value;
                final isLast = entry.key == HeatLevels.all.length - 1;
                return _buildHeatLevelOption(heatLevel, isLast: isLast);
              }),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'CANCEL',
                  style: TextStyle(color: isDark ? Colors.white70 : Colors.black87),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  // Use theme-aware colors consistent with the app's design system.
                  backgroundColor: isDark ? const Color.fromRGBO(255, 107, 53, 1) : Colors.orange,
                  foregroundColor: Colors.white,
                ),
                onPressed: _selectedLevel != null
                    ? () {
                        widget.onHeatLevelChanged(_selectedLevel);
                      }
                    : null,
                child: const Text('CONFIRM'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeatLevelOption(HeatLevel heatLevel, {bool isLast = false}) {
  final isSelected = _selectedLevel == heatLevel.name;
  // Prefer checking the dialog background color to decide if the dialog is dark
  final bool isDialogDark = Theme.of(context).dialogBackgroundColor.computeLuminance() < 0.5;
  final isDark = Theme.of(context).brightness == Brightness.dark || isDialogDark;

  // More dynamic: use the heat icon color for selected fills (with opacity)

  // More robust detection: check several theme background colors so we
  // correctly detect dark dialogs even when brightness is not set to dark.
  final theme = Theme.of(context);
  bool isColorDark(Color? c) => c != null && c.computeLuminance() < 0.5;
  final bool bgDark = isColorDark(theme.dialogTheme.backgroundColor) ||
      isColorDark(theme.colorScheme.surface) ||
      isColorDark(theme.colorScheme.surface) ||
      isColorDark(theme.scaffoldBackgroundColor);
  final bool effectiveDark = isDark || bgDark;

  // In dark mode we prefer transparent unselected backgrounds so the dialog's
  // dark/teal surface shows through (matches sauce-selection behavior).
  // Selected background uses the icon color with a modest opacity so the
  // fill matches the icon visually (e.g., green for mild, red for hot).
  // Use different opacities for dark/light contexts for better contrast.
  const int selectedLightAlpha = 40; // ~16% alpha
  const int selectedDarkAlpha = 60; // slightly more visible in dark

  final Color bgColor = isSelected
    ? (effectiveDark ? heatLevel.color.withAlpha(selectedDarkAlpha) : heatLevel.color.withAlpha(selectedLightAlpha))
    : (effectiveDark ? Colors.transparent : Colors.white);

  final Color borderColor = isSelected
    ? heatLevel.color
    : (effectiveDark ? theme.colorScheme.onSurface.withAlpha(140) : Theme.of(context).colorScheme.outline);

  final Color nameColor = isSelected ? theme.colorScheme.onPrimary : (effectiveDark ? Colors.white : Theme.of(context).colorScheme.onSurface);

  final Color descColor = effectiveDark ? Colors.white70 : Theme.of(context).colorScheme.onSurface.withAlpha(179);

    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 0 : 10),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedLevel = heatLevel.name;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: borderColor,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                heatLevel.icon,
                color: heatLevel.color,
                size: 24,
              ),
              const SizedBox(width: 12),
              Row(
                children: HeatLevels.buildFlameRating(heatLevel.stars, size: 16),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      heatLevel.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: nameColor,
                      ),
                    ),
                    Text(
                      heatLevel.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: descColor,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? heatLevel.color : (effectiveDark ? Colors.transparent : Colors.white),
                  border: Border.all(
                    color: isSelected ? heatLevel.color : (effectiveDark ? Colors.white70 : Theme.of(context).colorScheme.outline),
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 14,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
}