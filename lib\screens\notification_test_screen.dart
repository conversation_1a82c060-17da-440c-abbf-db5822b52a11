// 🧪 Notification Test Screen
// Test and demonstrate LOCAL notification functionality

import 'package:flutter/material.dart';
import '../services/notification_service.dart';
import '../services/notification_service_local_only.dart' as local_only_service;

class NotificationTestScreen extends StatefulWidget {
  const NotificationTestScreen({super.key});

  @override
  State<NotificationTestScreen> createState() => _NotificationTestScreenState();
}

class _NotificationTestScreenState extends State<NotificationTestScreen> {
  final NotificationService _notificationService = local_only_service.LocalOnlyNotificationService();

  bool _isLoading = false;
  bool _notificationsEnabled = true;
  String _status = 'Ready to test local notifications';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    try {
      final enabled = await _notificationService.areNotificationsEnabled();
      setState(() {
        _notificationsEnabled = enabled;
        _status = enabled
          ? 'Local notifications are ENABLED ✅'
          : 'Local notifications are DISABLED ❌';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Error loading settings: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleNotifications(bool enabled) async {
    try {
      await _notificationService.setNotificationsEnabled(enabled);
      setState(() {
        _notificationsEnabled = enabled;
        _status = enabled
          ? 'Notifications ENABLED! Daily reminders at 6 PM.'
          : 'Notifications DISABLED.';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _sendTestNotification() async {
    setState(() {
      _isLoading = true;
      _status = 'Sending test notification...';
    });

    try {
      await _notificationService.sendTestNotification();
      setState(() {
        _status = 'Test notification sent! Check your notification panel. ✅';
      });
    } catch (e) {
      setState(() {
        _status = 'Error sending test: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _scheduleDailyNotification() async {
    setState(() {
      _isLoading = true;
      _status = 'Scheduling daily notification...';
    });

    try {
      await _notificationService.scheduleDailyFeedbackNotification();
      setState(() {
        _status = 'Daily notification scheduled for 6 PM! ✅';
      });
    } catch (e) {
      setState(() {
        _status = 'Error scheduling: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Notification Test'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Status Card
                  Card(
                    color: _notificationsEnabled ? Colors.green[50] : Colors.red[50],
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          Icon(
                            _notificationsEnabled
                                ? Icons.notifications_active
                                : Icons.notifications_off,
                            size: 48,
                            color: _notificationsEnabled ? Colors.green : Colors.red,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            _notificationsEnabled
                                ? 'Notifications ENABLED'
                                : 'Notifications DISABLED',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _notificationsEnabled ? Colors.green : Colors.red,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _status,
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Toggle Switch
                  Card(
                    child: SwitchListTile(
                      title: const Text(
                        'Daily Feedback Notifications',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: const Text('Get reminded to share feedback and earn rewards'),
                      value: _notificationsEnabled,
                      onChanged: _toggleNotifications,
                      activeThumbColor: Colors.green,
                      secondary: const Icon(Icons.schedule),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Test Buttons
                  Text(
                    'Test Functions',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),

                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _sendTestNotification,
                    icon: const Icon(Icons.send),
                    label: const Text('Send Test Notification'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                    ),
                  ),

                  const SizedBox(height: 12),

                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _scheduleDailyNotification,
                    icon: const Icon(Icons.schedule),
                    label: const Text('Schedule Daily Notification'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                    ),
                  ),

                  const SizedBox(height: 12),

                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(context, '/loyalty');
                    },
                    icon: const Icon(Icons.star),
                    label: const Text('Go to Loyalty Page'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Info Section
                  Card(
                    color: Colors.blue[50],
                    child: const Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info, color: Colors.blue),
                              SizedBox(width: 8),
                              Text(
                                'How to Test',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                          Text('1. Enable notifications using the toggle above'),
                          SizedBox(height: 4),
                          Text('2. Tap "Send Test Notification" to see an immediate notification'),
                          SizedBox(height: 4),
                          Text('3. Tap the notification to navigate to the loyalty page'),
                          SizedBox(height: 4),
                          Text('4. Daily notifications are scheduled for 6 PM every day'),
                          SizedBox(height: 12),
                          Text(
                            'Note: Deep linking works best on mobile devices. On desktop, notifications will still appear but tapping them may not navigate automatically.',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontStyle: FontStyle.italic,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
