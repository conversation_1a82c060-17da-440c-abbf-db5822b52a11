import 'package:flutter/material.dart';
import '../widgets/unified_loyalty_dashboard.dart';

/// 🏆 Loyalty Screen
/// Main screen for loyalty program features
class LoyaltyScreen extends StatefulWidget {
  const LoyaltyScreen({super.key});

  @override
  State<LoyaltyScreen> createState() => _LoyaltyScreenState();
}

class _LoyaltyScreenState extends State<LoyaltyScreen> {
  // Mock user ID for testing - in production this would come from auth service
  static const String _userId = 'test_user_123';

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'LOYALTY REWARDS',
          style: Theme.of(context).appBarTheme.titleTextStyle,
        ),
        backgroundColor: isDark
            ? const Color(0xFF1a3d3d) // Dark teal surface
            : Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              // Navigate to full transaction history
              _showTransactionHistory();
            },
            tooltip: 'Transaction History',
          ),
          IconButton(
            icon: const Icon(Icons.card_giftcard),
            onPressed: () {
              // Navigate to rewards catalog
              _showRewardsCatalog();
            },
            tooltip: 'Rewards Catalog',
          ),
        ],
      ),
      body: const UnifiedLoyaltyDashboard(
        userId: _userId,
      ),
    );
  }

  /// 📋 Show transaction history
  void _showTransactionHistory() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    const Icon(Icons.history, color: Colors.orange),
                    const SizedBox(width: 8),
                    const Text(
                      'Transaction History',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const Divider(),
              // Transaction list
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  itemCount: 10, // Mock data
                  itemBuilder: (context, index) => _buildTransactionTile(index),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎁 Show rewards catalog
  void _showRewardsCatalog() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.9,
        minChildSize: 0.6,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: isDark
                ? const Color(0xFF1a3d3d) // Dark teal surface
                : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[600] : Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Icon(
                      Icons.card_giftcard,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Rewards Catalog',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.close,
                        color: isDark ? Colors.white : Theme.of(context).colorScheme.onSurface,
                      ),
                      tooltip: 'Close rewards catalog',
                    ),
                  ],
                ),
              ),
              Divider(
                color: isDark ? Colors.grey[600] : Colors.grey[300],
              ),
              // Rewards grid
              Expanded(
                child: GridView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: 8, // Mock data
                  itemBuilder: (context, index) => _buildRewardCard(index),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 📋 Build transaction tile
  Widget _buildTransactionTile(int index) {
    final isEarned = index % 2 == 0;
    final points = isEarned ? 25 : -50;
    final description = isEarned
        ? 'Points earned from order #${1000 + index}'
        : 'Points redeemed for discount';
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final color = isEarned ? const Color.fromARGB(255, 135, 136, 90) : const Color(0xFFFF6B35);

    return Semantics(
      label: '$description, ${isEarned ? 'earned' : 'spent'} $points points',
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        color: isDark
            ? const Color(0xFF2d4a4a) // Dark teal card background
            : Colors.white,
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: color.withValues(alpha: isDark ? 0.2 : 0.1),
            child: Icon(
              isEarned ? Icons.add : Icons.remove,
              color: color,
              semanticLabel: isEarned ? 'Points earned' : 'Points spent',
            ),
          ),
          title: Text(
            description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isDark ? Colors.white : Theme.of(context).colorScheme.onSurface,
            ),
          ),
          subtitle: Text(
            DateTime.now().subtract(Duration(days: index)).toString().split(' ')[0],
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isDark
                  ? Colors.white.withValues(alpha: 0.7)
                  : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          trailing: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              '${isEarned ? '+' : ''}$points pts',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 🎁 Build reward card
  Widget _buildRewardCard(int index) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    final rewards = [
      {'name': 'Free Drink', 'points': 500, 'icon': Icons.local_drink, 'color': const Color(0xFF2196F3)},
      {'name': 'Free Side', 'points': 750, 'icon': Icons.restaurant, 'color': const Color.fromARGB(255, 135, 136, 90)},
      {'name': '10% Off', 'points': 1000, 'icon': Icons.discount, 'color': const Color(0xFFFF6B35)},
      {'name': 'Free Sandwich', 'points': 1500, 'icon': Icons.lunch_dining, 'color': const Color(0xFF9B1C24)},
      {'name': 'Free Dessert', 'points': 600, 'icon': Icons.cake, 'color': const Color(0xFFE91E63)},
      {'name': '15% Off', 'points': 1250, 'icon': Icons.percent, 'color': const Color(0xFF9C27B0)},
      {'name': 'Free Combo', 'points': 2000, 'icon': Icons.fastfood, 'color': const Color(0xFF3F51B5)},
      {'name': 'Birthday Special', 'points': 0, 'icon': Icons.celebration, 'color': const Color(0xFFFFCC72)},
    ];

    final reward = rewards[index % rewards.length];
    final canAfford = index < 3; // Mock affordability

    return Card(
      elevation: canAfford ? 4 : 1,
      color: isDark
          ? const Color(0xFF2d4a4a) // Dark teal card background
          : Colors.white,
      child: InkWell(
        onTap: canAfford ? () => _redeemReward(reward) : null,
        borderRadius: BorderRadius.circular(12),
        child: Semantics(
          label: '${reward['name']}, ${reward['points']} points, ${canAfford ? 'affordable' : 'not affordable'}',
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  reward['icon'] as IconData,
                  size: 48,
                  color: canAfford
                      ? reward['color'] as Color
                      : (isDark ? Colors.grey[600] : Colors.grey),
                  semanticLabel: '${reward['name']} icon',
                ),
                const SizedBox(height: 12),
                Text(
                  reward['name'] as String,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: canAfford
                        ? (isDark ? Colors.white : Theme.of(context).colorScheme.onSurface)
                        : (isDark ? Colors.grey[600] : Colors.grey),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                if (reward['points'] as int > 0) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: (reward['color'] as Color).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: (reward['color'] as Color).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      '${reward['points']} pts',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: canAfford
                            ? reward['color'] as Color
                            : (isDark ? Colors.grey[600] : Colors.grey),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ] else ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFCC72).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'FREE',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: const Color(0xFFFFCC72),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                const SizedBox(height: 12),
                if (canAfford) ...[
                  ElevatedButton(
                    onPressed: () => _redeemReward(reward),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: reward['color'] as Color,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 36),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'REDEEM',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ] else ...[
                  Container(
                    width: double.infinity,
                    height: 36,
                    decoration: BoxDecoration(
                      color: isDark ? Colors.grey[700] : Colors.grey[300],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        'INSUFFICIENT POINTS',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: isDark ? Colors.grey[500] : Colors.grey,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 🎁 Redeem reward
  void _redeemReward(Map<String, dynamic> reward) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark
            ? const Color(0xFF2d4a4a) // Dark teal card background
            : Colors.white,
        title: Row(
          children: [
            Icon(
              reward['icon'] as IconData,
              color: reward['color'] as Color,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Redeem ${reward['name']}?',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: isDark ? Colors.white : Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'You are about to redeem:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isDark ? Colors.white : Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              reward['name'] as String,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18,
                color: isDark ? Colors.white : Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            if (reward['points'] as int > 0)
              Text(
                'Cost: ${reward['points']} points',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isDark ? Colors.white.withValues(alpha: 0.8) : Theme.of(context).colorScheme.onSurface,
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'CANCEL',
              style: TextStyle(
                color: isDark ? Colors.white.withValues(alpha: 0.8) : Theme.of(context).primaryColor,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showRedemptionSuccess(reward);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: reward['color'] as Color,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'REDEEM',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  /// ✅ Show redemption success
  void _showRedemptionSuccess(Map<String, dynamic> reward) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '${reward['name']} redeemed successfully!',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: const Color.fromARGB(255, 135, 136, 90), // Pickle green for success
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: 'VIEW',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to order or rewards history
          },
        ),
      ),
    );
  }
}
