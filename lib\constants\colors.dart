import 'package:flutter/material.dart';

class AppColors {
  // Primary Brand Colors
  static const chicaOrange = Color(0xFFFF6B35); // Sunburst Yellow
  static const spiceRed = Color(0xFF9B1C24); // Pantone 7625 C
  static const sunburstYellow = Color(0xFFFFCC72); // Pantone 123C
  static const pickleGreen = Color.fromARGB(255, 135, 136, 90);


  // Convenience aliases for common usage
  static const primary = Color.fromRGBO(255, 107, 53, 1); //Sunburst Yellow
  static const secondary = spiceRed;

  // Usage Guidelines
  // Chica Orange: Primary brand color, used for buttons and primary actions
  // Spice Red: Used sparingly for highlights, alerts and promotional badges
  // Sunburst Yellow: For backgrounds, overlays, and secondary accents
  // Pickle Green: For secondary calls-to-action and icon strokes

  // Text Colors
  static const textPrimary = Color(0xFF7A3B00);
  static const textSecondary = Color(0xFF757575);
  static const textDisabled = Color(0xFFBDBDBD);


  // Background Colors
  static const background = Colors.white; // Neutral Tan
  static const surfaceLight = Colors.white; // Neutral Tan
  static const surfaceMedium = Colors.white; // Neutral Tan

  // Status Colors
  static const success = Color.fromARGB(255, 135, 136, 90);
  static const warning = Color(0xFFFFC107);
  static const error = spiceRed; // Pantone 7625 C
}

/// Global configuration for food item text colors
/// This allows easy customization of text colors across all food item displays
class FoodItemTextColors {
  // Item name colors
  static Color itemNameColor = const Color.fromARGB(255, 135, 136, 90);
  static Color itemNameColorLight = Colors.white;

  // Item description colors
  static Color itemDescriptionColor = Colors.grey[600]!;
  static Color itemDescriptionColorLight = Colors.grey[300]!;

  // Item price colors
  static Color itemPriceColor = const Color.fromRGBO(255, 107, 53, 1);
  static Color itemPriceColorAlternate = AppColors.spiceRed;

  // Special/Popular badge colors
  static Color specialBadgeColor = AppColors.spiceRed;
  static Color popularBadgeColor = AppColors.chicaOrange;

  /// Update all item name colors globally
  static void updateItemNameColors({
    Color? primary,
    Color? light,
  }) {
    if (primary != null) itemNameColor = primary;
    if (light != null) itemNameColorLight = light;
  }

  /// Update all item description colors globally
  static void updateItemDescriptionColors({
    Color? primary,
    Color? light,
  }) {
    if (primary != null) itemDescriptionColor = primary;
    if (light != null) itemDescriptionColorLight = light;
  }

  /// Update all item price colors globally
  static void updateItemPriceColors({
    Color? primary,
    Color? alternate,
  }) {
    if (primary != null) itemPriceColor = primary;
    if (alternate != null) itemPriceColorAlternate = alternate;
  }

  /// Update special/popular badge colors globally
  static void updateBadgeColors({
    Color? special,
    Color? popular,
  }) {
    if (special != null) specialBadgeColor = special;
    if (popular != null) popularBadgeColor = popular;
  }

  /// Reset all colors to defaults
  static void resetToDefaults() {
    itemNameColor = Colors.black87;
    itemNameColorLight = Colors.white;
    itemDescriptionColor = Colors.grey[600]!;
    itemDescriptionColorLight = Colors.grey[300]!;
    itemPriceColor = AppColors.chicaOrange;
    itemPriceColorAlternate = AppColors.spiceRed;
    specialBadgeColor = AppColors.spiceRed;
    popularBadgeColor = AppColors.chicaOrange;
  }
}
