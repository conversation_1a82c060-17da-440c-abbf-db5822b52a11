import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/unified_loyalty_service.dart';
import '../models/loyalty_program.dart';
import '../constants/colors.dart';
import '../constants/typography.dart';

/// 🏆 Unified Loyalty Dashboard Widget
/// 
/// Displays comprehensive loyalty information including:
/// - Current points and tier status
/// - Game-earned vs purchase-earned points breakdown
/// - Daily challenges and streak information
/// - Active multiplier events
/// - Recent transactions with source tracking
class UnifiedLoyaltyDashboard extends StatefulWidget {
  final String userId;

  const UnifiedLoyaltyDashboard({
    super.key,
    required this.userId,
  });

  @override
  State<UnifiedLoyaltyDashboard> createState() => _UnifiedLoyaltyDashboardState();
}

class _UnifiedLoyaltyDashboardState extends State<UnifiedLoyaltyDashboard> {
  final UnifiedLoyaltyService _loyaltyService = UnifiedLoyaltyService();
  LoyaltyAccount? _account;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLoyaltyData();
  }

  Future<void> _loadLoyaltyData() async {
    try {
      await _loyaltyService.initialize();
      final account = await _loyaltyService.getLoyaltyAccount(widget.userId);
      setState(() {
        _account = account;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).primaryColor,
          ),
        ),
      );
    }

    if (_account == null) {
      return Center(
        child: Text(
          'Unable to load loyalty information',
          style: AppTypography.bodyLarge.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main loyalty card
          _buildLoyaltyCard(),
          const SizedBox(height: 20),
          
          // Points breakdown
          _buildPointsBreakdown(),
          const SizedBox(height: 20),
          
          // Daily challenges
          _buildDailyChallenges(),
          const SizedBox(height: 20),
          
          // Active events
          _buildActiveEvents(),
          const SizedBox(height: 20),
          
          // Recent transactions
          _buildRecentTransactions(),
        ],
      ),
    );
  }

  Widget _buildLoyaltyCard() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final tierColor = _getTierColor(_account!.tier);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark
              ? [
                  const Color(0xFF1a3d3d), // Dark teal surface
                  const Color(0xFF0d2626), // Darker teal background
                ]
              : [
                  tierColor,
                  tierColor.withValues(alpha: 0.8),
                ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : tierColor.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Semantics(
        label: 'Loyalty card showing ${_account!.currentPoints} points, ${_account!.tier.name} tier member',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_account!.currentPoints}',
                        style: AppTypography.displaySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        semanticsLabel: '${_account!.currentPoints} loyalty points',
                      ),
                      Text(
                        'LOYALTY POINTS',
                        style: AppTypography.labelMedium.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                          letterSpacing: 1.0,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isDark
                        ? AppColors.primary.withValues(alpha: 0.2)
                        : Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: isDark
                        ? Border.all(color: AppColors.primary.withValues(alpha: 0.3))
                        : null,
                  ),
                  child: Text(
                    _account!.tier.name.toUpperCase(),
                    style: AppTypography.labelSmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                    semanticsLabel: '${_account!.tier.name} tier member',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Worth \$${_account!.dollarValue.toStringAsFixed(2)} • ${_account!.daysAsMember} days member',
              style: AppTypography.bodyMedium.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
              ),
              semanticsLabel: 'Points worth ${_account!.dollarValue.toStringAsFixed(2)} dollars, member for ${_account!.daysAsMember} days',
            ),
          ],
        ),
      ),
    ).animate()
      .fadeIn()
      .slideY(begin: -0.2, end: 0);
  }

  Widget _buildPointsBreakdown() {
    final gamePoints = _getGamePointsToday();
    final purchasePoints = _getPurchasePointsToday();
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark
            ? const Color(0xFF2d4a4a) // Dark teal card background
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Semantics(
        label: 'Today\'s points breakdown: $gamePoints from games, $purchasePoints from purchases',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'TODAY\'S POINTS',
              style: AppTypography.headlineSmall.copyWith(
                color: isDark ? Colors.white : AppColors.textPrimary,
                fontSize: 18,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildPointsSource(
                    icon: Icons.sports_esports,
                    label: 'Games',
                    points: gamePoints,
                    color: AppColors.chicaOrange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildPointsSource(
                    icon: Icons.shopping_bag,
                    label: 'Purchases',
                    points: purchasePoints,
                    color: AppColors.pickleGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Semantics(
              label: 'Daily game progress: $gamePoints out of 500 points',
              child: LinearProgressIndicator(
                value: gamePoints / 500, // Daily game limit
                backgroundColor: isDark
                    ? Colors.grey.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.2),
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.chicaOrange),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Daily game limit: $gamePoints/500 points',
              style: AppTypography.bodySmall.copyWith(
                color: isDark
                    ? Colors.white.withValues(alpha: 0.7)
                    : AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    ).animate()
      .fadeIn(delay: const Duration(milliseconds: 200))
      .slideY(begin: 0.2, end: 0);
  }

  Widget _buildPointsSource({
    required IconData icon,
    required String label,
    required int points,
    required Color color,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Semantics(
      label: '$points points from $label',
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: isDark ? 0.15 : 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: isDark ? 0.4 : 0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
              semanticLabel: '$label icon',
            ),
            const SizedBox(height: 8),
            Text(
              '$points',
              style: AppTypography.titleLarge.copyWith(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: AppTypography.bodySmall.copyWith(
                color: isDark
                    ? Colors.white.withValues(alpha: 0.7)
                    : AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyChallenges() {
    final challenges = _loyaltyService.getDailyChallenges();
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark
            ? const Color(0xFF2d4a4a) // Dark teal card background
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Semantics(
        label: 'Daily challenges section with ${challenges.length} challenges',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'DAILY CHALLENGES',
              style: AppTypography.headlineSmall.copyWith(
                fontSize: 18,
                color: isDark ? Colors.white : AppColors.textPrimary,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 16),
            ...challenges.map((challenge) => _buildChallengeItem(challenge)),
          ],
        ),
      ),
    ).animate()
      .fadeIn(delay: const Duration(milliseconds: 400))
      .slideY(begin: 0.2, end: 0);
  }

  Widget _buildChallengeItem(DailyChallenge challenge) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    const completedColor = AppColors.pickleGreen;
    final incompleteColor = isDark ? Colors.grey[400]! : Colors.grey[600]!;

    return Semantics(
      label: '${challenge.name}, ${challenge.description}, ${challenge.isCompleted ? 'completed' : 'not completed'}, ${challenge.pointsReward} points reward',
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: challenge.isCompleted
              ? completedColor.withValues(alpha: isDark ? 0.15 : 0.1)
              : (isDark ? Colors.grey[800]! : Colors.grey[100]!).withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: challenge.isCompleted
                ? completedColor.withValues(alpha: isDark ? 0.4 : 0.3)
                : incompleteColor.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              challenge.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
              color: challenge.isCompleted ? completedColor : incompleteColor,
              semanticLabel: challenge.isCompleted ? 'Challenge completed' : 'Challenge not completed',
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    challenge.name,
                    style: AppTypography.titleMedium.copyWith(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    challenge.description,
                    style: AppTypography.bodySmall.copyWith(
                      color: isDark
                          ? Colors.white.withValues(alpha: 0.7)
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: challenge.isCompleted
                    ? completedColor.withValues(alpha: 0.2)
                    : AppColors.chicaOrange.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '+${challenge.pointsReward}',
                style: AppTypography.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: challenge.isCompleted ? completedColor : AppColors.chicaOrange,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveEvents() {
    final multiplierEvent = _loyaltyService.getActiveMultiplierEvent();
    final streak = _loyaltyService.currentStreak;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    if (multiplierEvent == null && streak <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark
            ? const Color(0xFF2d4a4a) // Dark teal card background
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Semantics(
        label: 'Active bonuses section',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ACTIVE BONUSES',
              style: AppTypography.headlineSmall.copyWith(
                fontSize: 18,
                color: isDark ? Colors.white : AppColors.textPrimary,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 16),

            if (multiplierEvent != null && multiplierEvent.isActive) ...[
              _buildEventItem(
                icon: Icons.flash_on,
                title: multiplierEvent.name,
                description: multiplierEvent.description,
                color: AppColors.sunburstYellow,
              ),
            ],

            if (streak > 1) ...[
              _buildEventItem(
                icon: Icons.local_fire_department,
                title: '$streak-Day Streak',
                description: 'Keep playing daily for bigger bonuses!',
                color: AppColors.chicaOrange,
              ),
            ],
          ],
        ),
      ),
    ).animate()
      .fadeIn(delay: const Duration(milliseconds: 600))
      .slideY(begin: 0.2, end: 0);
  }

  Widget _buildEventItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Semantics(
      label: '$title: $description',
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: isDark ? 0.15 : 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: isDark ? 0.4 : 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
              semanticLabel: '$title icon',
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.titleMedium.copyWith(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    description,
                    style: AppTypography.bodySmall.copyWith(
                      color: isDark
                          ? Colors.white.withValues(alpha: 0.7)
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    final transactions = _loyaltyService.transactionHistory.take(5).toList();
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark
            ? const Color(0xFF2d4a4a) // Dark teal card background
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Semantics(
        label: 'Recent activity section with ${transactions.length} transactions',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'RECENT ACTIVITY',
              style: AppTypography.headlineSmall.copyWith(
                fontSize: 18,
                color: isDark ? Colors.white : AppColors.textPrimary,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 16),

            if (transactions.isEmpty) ...[
              Text(
                'No recent activity',
                style: AppTypography.bodyMedium.copyWith(
                  color: isDark
                      ? Colors.white.withValues(alpha: 0.7)
                      : AppColors.textSecondary,
                ),
              ),
            ] else ...[
              ...transactions.map((transaction) => _buildTransactionItem(transaction)),
            ],
          ],
        ),
      ),
    ).animate()
      .fadeIn(delay: const Duration(milliseconds: 800))
      .slideY(begin: 0.2, end: 0);
  }

  Widget _buildTransactionItem(PointsTransaction transaction) {
    final isPositive = transaction.points > 0;
    final icon = _getTransactionIcon(transaction.type);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final color = isPositive ? AppColors.pickleGreen : AppColors.spiceRed;

    return Semantics(
      label: '${transaction.description}, ${_formatDate(transaction.createdAt)}, ${isPositive ? 'earned' : 'spent'} ${transaction.points} points',
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: isDark ? 0.15 : 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: isDark ? 0.3 : 0.2),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
              semanticLabel: '${transaction.type} transaction icon',
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.description,
                    style: AppTypography.bodyMedium.copyWith(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: isDark ? Colors.white : AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    _formatDate(transaction.createdAt),
                    style: AppTypography.bodySmall.copyWith(
                      fontSize: 11,
                      color: isDark
                          ? Colors.white.withValues(alpha: 0.7)
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                '${isPositive ? '+' : ''}${transaction.points}',
                style: AppTypography.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  Color _getTierColor(LoyaltyTier tier) {
    switch (tier) {
      case LoyaltyTier.bronze:
        return const Color(0xFFCD7F32);
      case LoyaltyTier.silver:
        return const Color(0xFFC0C0C0);
      case LoyaltyTier.gold:
        return const Color(0xFFFFD700);
      case LoyaltyTier.platinum:
        return const Color(0xFFE5E4E2);
      case LoyaltyTier.diamond:
        return const Color(0xFFB9F2FF);
    }
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'game_completion':
        return Icons.sports_esports;
      case 'purchase':
        return Icons.shopping_bag;
      case 'daily_challenge':
        return Icons.emoji_events;
      case 'streak_bonus':
        return Icons.local_fire_department;
      case 'multiplier_bonus':
        return Icons.flash_on;
      case 'redemption':
        return Icons.redeem;
      default:
        return Icons.star;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else {
      return '${difference.inDays} days ago';
    }
  }

  int _getGamePointsToday() {
    return _loyaltyService.dailyGamePointsEarned;
  }

  int _getPurchasePointsToday() {
    final today = DateTime.now();
    return _loyaltyService.transactionHistory
        .where((t) =>
            t.type == 'purchase' &&
            t.createdAt.year == today.year &&
            t.createdAt.month == today.month &&
            t.createdAt.day == today.day)
        .fold(0, (sum, t) => sum + t.points);
  }
}
