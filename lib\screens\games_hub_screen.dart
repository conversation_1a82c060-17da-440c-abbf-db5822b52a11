import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:qsr_app/themes/app_theme.dart';
import 'package:provider/provider.dart';

// Define colors from the HTML style for easy reuse
import 'package:qsr_app/constants/colors.dart';
const Color sunburstYellow = Color(0xFFF8B133);
const Color pickleGreen = Color(0xFF798D63);

class GamesHubScreen extends StatelessWidget {
  const GamesHubScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDarkMode ? Colors.white.with<PERSON><PERSON>pha(128) : Colors.black.withAlpha(128),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.arrow_back,
              color: theme.appBarTheme.foregroundColor,
              size: 20,
            ),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'GAMES & REWARDS',
          style: theme.appBarTheme.titleTextStyle,
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        foregroundColor: theme.appBarTheme.foregroundColor,
        elevation: theme.appBarTheme.elevation,
        actions: [
          IconButton(
            icon: Icon(
              isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.appBarTheme.iconTheme?.color,
            ),
            onPressed: () {
              Provider.of<AppTheme>(context, listen: false).toggleTheme();
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            _buildDashboard(context),
            const SizedBox(height: 64),
            _buildFeaturedGames(context),
            const SizedBox(height: 64),
            _buildPhotoChallenge(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboard(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      color: theme.cardTheme.color,
      elevation: theme.cardTheme.elevation,
      shadowColor: theme.cardTheme.shadowColor,
      shape: theme.cardTheme.shape,
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'My Dashboard',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontFamily: 'MontserratBlack',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Accumulated Points',
                      style: theme.textTheme.bodySmall,
                    ),
                    Text(
                      '1,250',
                      style: theme.textTheme.displaySmall?.copyWith(
                        fontWeight: FontWeight.w900,
                        color: sunburstYellow, // Keep sunburstYellow for accent
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Prizes Collected',
                      style: theme.textTheme.bodySmall,
                    ),
                    Text(
                      'Free Fries',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: pickleGreen, // Keep pickleGreen for accent
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: LinearProgressIndicator(
                value: 0.75,
                minHeight: 8,
                backgroundColor: theme.dividerTheme.color?.withAlpha(128),
                valueColor: const AlwaysStoppedAnimation<Color>(sunburstYellow),
              ),
            ),
            const SizedBox(height: 4),
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                '250 points to next prize!',
                style: theme.textTheme.bodySmall,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedGames(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Featured Games',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontFamily: 'SofiaRoughBlackThree',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildGameCard(
          context,
          imageUrl: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDHLSjAAiLQ3x5QegCQDc_pI_6jLhluyzItQTCTYlixfuEr7ouRlLFrIWIQKXhNb3j-LogCox24RXHwcG6YJqxErR64kyQsJQqks7UDxDdIfVMB6uCBrWVjuOKGCfKeBDyWdzyo6ZG2DgfbmwRpFC_09A1mMSWIZFKjBPhGcvJtdXeIv9hTr8x0SMsl5hSHW2jSsGw5baRmguTF2Y4VEeRBz9hjD8wqd5PS3tSyPRMBi7elvd0gHNRel1C_dn7EZH0fdpBY_cXDjA',
          title: 'Chicken Catch',
          description: 'Catch as many falling chicken pieces as you can!',
        ),
        const SizedBox(height: 16),
        _buildGameCard(
          context,
          imageUrl: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDOzaSXFi9rBONEKPIBA0e6fJhvEAlpn6llnyOATfkBoyHfNB-0AIuajZM3MxwKy-Xtw6D6hfPjwe4z49Oe4iwBBpXwipXFeJ1NTk41953La1kpPwqOBxh7U59hNC1sc6rxwIM9gV4Qmyt5sHv51cKj2UELvEZxWnhgwa4XJhanLIzXmpKR2Y2Qw9OOhdwQiX_i6R2w_16vswd_cSnFvCuIeHGIBate2fFRrcjh6uJ6EMxqFWzWCucISlkKXGTwwBNDEQzghwuO5A',
          title: 'Fry Frenzy',
          description: 'Sort the fries by size before time runs out.',
        ),
        const SizedBox(height: 16),
        _buildGameCard(
          context,
          imageUrl: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA9RVDpsuYAp3GvlzrF661-4eKRBLGWUd-CacGLeDWaUGPn9gdBoy053YaBYJ2vcTriTig7Lw0VJZMV8czDKoUqOHKqxHO5Xj0OxYj3EwZtID26bjl-6CxE6NVF5V3wIpl6k0iDSe5WYNlwSRqzHaUIF2BtE4amQKz44ifp6dCf_DS7oqJfYwy_9Ro1FUtMTnb68utGpfrsUTjQfSIovmJH8ixzUGv52ZE1RJXfPvNlutvL4bzwZM0otH00F1GyKUc1MijN-rMA3Q',
          title: 'Burger Stack',
          description: 'Build the tallest, most delicious chicken burger.',
        ),
      ],
    );
  }

  Widget _buildGameCard(BuildContext context, {
    required String imageUrl,
    required String title,
    required String description,
  }) {
    final theme = Theme.of(context);
    return Card(
      color: theme.cardTheme.color,
      elevation: theme.cardTheme.elevation,
      shadowColor: theme.cardTheme.shadowColor,
      shape: theme.cardTheme.shape,
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CachedNetworkImage(
            imageUrl: imageUrl,
            height: 200,
            width: double.infinity,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: 200,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.chicaOrange.withAlpha(25),
                    sunburstYellow.withAlpha(25),
                  ],
                ),
              ),
              child: Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                ),
              ),
            ),
            errorWidget: (context, url, error) => GestureDetector(
              onTap: () => CachedNetworkImage.evictFromCache(imageUrl),
              child: Container(
                height: 200,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.chicaOrange.withAlpha(25),
                      sunburstYellow.withAlpha(25),
                    ],
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.refresh,
                      color: theme.colorScheme.error,
                      size: 48,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap to retry',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: theme.textTheme.bodySmall,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  style: theme.elevatedButtonTheme.style?.copyWith(
                    minimumSize: WidgetStateProperty.all(const Size(double.infinity, 80)),
                  ),
                  onPressed: () {},
                  child: Text(
                    'Play Now',
                    style: theme.elevatedButtonTheme.style?.textStyle?.resolve(WidgetState.values.toSet())?.copyWith(
                      fontFamily: 'MontserratBlack',
                      fontSize: 20,
                    ),
                  ),
                ),
                const SizedBox(height: 40), // Increased for extra bottom padding
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoChallenge(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'PHOTO CHALLENGE',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontFamily: 'SofiaRoughBlackThree',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          color: theme.cardTheme.color,
          elevation: theme.cardTheme.elevation,
          shadowColor: theme.cardTheme.shadowColor,
          shape: theme.cardTheme.shape,
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                const Icon(
                  Icons.photo_camera,
                  color: sunburstYellow, // Keep sunburstYellow for accent
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(
                  'Capture the Moment',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontFamily: 'MontserratBlack',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Share a photo of your meal and earn bonus points!',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodySmall,
                ),
                const SizedBox(height: 40),
                ElevatedButton.icon(
                  style: theme.elevatedButtonTheme.style?.copyWith(
                    minimumSize: WidgetStateProperty.all(const Size(double.infinity, 80)),
                  ),
                  onPressed: () {},
                  icon: Icon(Icons.upload_file, color: theme.elevatedButtonTheme.style?.foregroundColor?.resolve(WidgetState.values.toSet())),
                  label: Text(
                    'Upload Photo/Video',
                    style: theme.elevatedButtonTheme.style?.textStyle?.resolve(WidgetState.values.toSet())?.copyWith(
                      fontFamily: 'MontserratBlack',
                      fontSize: 20,
                    ),
                  ),
                ),
                const SizedBox(height: 24), // Increased for extra bottom padding
              ],
            ),
          ),
        ),
      ],
    );
  }
}



